let data;
let scales = ["NLE","NC","NF","ID","BO","SC","BD","SEC","PR","EA","SA","MQ","RT","GT","SO","ER","GR","Con","Cong"]
let s_obj = {
  "1": "NLE",
  "2": "NC",
  "3": "NF",
  "4": "ID",
  "5": "BO",
  "6": "SC",
  "7": "BD",
  "8": "SEC",
  "9": "PR",
  "10": "EA",
  "11": "SA",
  "12": "MQ",
  "13": "RT",
  "14": "GT",
  "15": "SO",
  "16": "ER",
  "17": "GR",
  "999": "Con",
  "1000": "Cong"
}
class Report{
    __contructor(){

    }
createProfileBackgroundDomainContrastTable(data){

    let scales = data.scales;
    let low_n = data.lowNumberOfUsers;
    let mid_n = data.middleNroOfUsers;
    let hi_n = data.highNumberOfUsers;

    let get_row = (n)=>{
        const result = {};
        for (const key in scales) {
            result[key] = scales[key][n];
        }
        return result
    }
    
    let s_l = get_row(0)
    let s_m = get_row(1)
    let s_h =get_row(2)

return `
### Profile Contrast (divided in thirds on Optimal Profiles from Full scale scores)

Third|N|NLE|NC|NF|ID|BO|SC|BD|SEC|PR|EA|SA|MQ|RT|GT|SO|ER|GR|Con|Cong
--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--
Lowest|${low_n}|${s_l["1"]}|${s_l["2"]}|${s_l["3"]}|${s_l["4"]}|${s_l["5"]}|${s_l["6"]}|${s_l["7"]}|${s_l["8"]}|${s_l["9"]}|${s_l["10"]}|${s_l["11"]}|${s_l["12"]}|${s_l["13"]}|${s_l["14"]}|${s_l["15"]}|${s_l["16"]}|${s_l["17"]}|${s_l["999"]}|${s_l["1000"]}
Middle|${mid_n}|${s_m["1"]}|${s_m["2"]}|${s_m["3"]}|${s_m["4"]}|${s_m["5"]}|${s_m["6"]}|${s_m["7"]}|${s_m["8"]}|${s_m["9"]}|${s_m["10"]}|${s_m["11"]}|${s_m["12"]}|${s_m["13"]}|${s_m["14"]}|${s_m["15"]}|${s_m["16"]}|${s_m["17"]}|${s_m["999"]}|${s_m["1000"]}
Highest|${hi_n}|${s_h["1"]}|${s_h["2"]}|${s_h["3"]}|${s_h["4"]}|${s_h["5"]}|${s_h["6"]}|${s_h["7"]}|${s_h["8"]}|${s_h["9"]}|${s_h["10"]}|${s_h["11"]}|${s_h["12"]}|${s_h["13"]}|${s_h["14"]}|${s_h["15"]}|${s_h["16"]}|${s_h["17"]}|${s_h["999"]}|${s_h["1000"]}
`
}

createBackgroundDomainContrastTable(data){
    
    let scales = data.scales;
    let low_n = data.lowNumberOfUsers;
    let mid_n = data.middleNroOfUsers;
    let hi_n = data.highNumberOfUsers;

    let get_row = (n)=>{
        const result = {};
        for (const key in scales) {
            result[key] = scales[key][n];
        }
        return result
    }
    
    let s_l = get_row(0)
    let s_m = get_row(1)
    let s_h =get_row(2)

`
### Profile Contrast (divided in thirds on Optimal Profiles from Full scale scores)

Third|N|NLE|NC|NF|ID|BO|SC|BD|SEC|PR|EA|SA|MQ|RT|GT|SO|ER|GR|Con|Cong
--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--
Lowest|${low_n}|${s_l["1"]}|${s_l["2"]}|${s_l["3"]}|${s_l["4"]}|${s_l["5"]}|${s_l["6"]}|${s_l["7"]}|${s_l["8"]}|${s_l["9"]}|${s_l["10"]}|${s_l["11"]}|${s_l["12"]}|${s_l["13"]}|${s_l["14"]}|${s_l["15"]}|${s_l["16"]}|${s_l["17"]}|${s_l["999"]}|${s_l["1000"]}
Middle|${mid_n}|${s_m["1"]}|${s_m["2"]}|${s_m["3"]}|${s_m["4"]}|${s_m["5"]}|${s_m["6"]}|${s_m["7"]}|${s_m["8"]}|${s_m["9"]}|${s_m["10"]}|${s_m["11"]}|${s_m["12"]}|${s_m["13"]}|${s_m["14"]}|${s_m["15"]}|${s_m["16"]}|${s_m["17"]}|${s_m["999"]}|${s_m["1000"]}
Highest|${hi_n}|${s_h["1"]}|${s_h["2"]}|${s_h["3"]}|${s_h["4"]}|${s_h["5"]}|${s_h["6"]}|${s_h["7"]}|${s_h["8"]}|${s_h["9"]}|${s_h["10"]}|${s_h["11"]}|${s_h["12"]}|${s_h["13"]}|${s_h["14"]}|${s_h["15"]}|${s_h["16"]}|${s_h["17"]}|${s_h["999"]}|${s_h["1000"]}
`
}

createAggregateProfileTable(data){
    
    let s = data.scales;

return `
### Aggregate Scale Scores

NLE|NC|NF|ID|BO|SC|BD|SEC|PR|EA|SA|MQ|RT|GT|SO|ER|GR|Con|Cong
--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--
${s["1"]}|${s["2"]}|${s["3"]}|${s["4"]}|${s["5"]}|${s["6"]}|${s["7"]}|${s["8"]}|${s["9"]}|${s["10"]}|${s["11"]}|${s["12"]}|${s["13"]}|${s["14"]}|${s["15"]}|${s["16"]}|${s["17"]}|${s["999"]}|${s["1000"]}
`
}
createDecileProfileTable(scales){
let result =
`
### Decile Profile (%)
Scale|1|2|3|4|5|6|7|8|9|10
--|--|--|--|--|--|--|--|--|--|--  
`
    const entries = Object.entries(s_obj);
    for (const [key, value] of entries) {
        result+= `${value}|${scales[key].join('|')}\n`
    }
    return result;
        
}

createComparativeByIndividualReportTable(data){

    let result = 
`
Name|NLE|NC|NF|ID|BO|SC|BD|SEC|PR|EA|SA|MQ|RT|GT|SO|ER|GR|Con|Cong
--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--
`
//   console.log(data)
    data.forEach((item, index) =>{
        let s = item.aggregateProfile.scales
        result+=`${item.individualAlias}|${s["1"]}|${s["2"]}|${s["3"]}|${s["4"]}|${s["5"]}|${s["6"]}|${s["7"]}|${s["8"]}|${s["9"]}|${s["10"]}|${s["11"]}|${s["12"]}|${s["13"]}|${s["14"]}|${s["15"]}|${s["16"]}|${s["17"]}|${s["999"]}|${s["1000"]}\n`

    })


    return result

}

createIndividualReport(data){


    let result = 
`
On Screen|Date|NLE|NC|NF|ID|BO|SC|BD|SEC|PR|EA|SA|MQ|RT|GT|SO|ER|GR|Con|Cong
--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--
`;
    data.forEach((item, index) =>{
        let s = item.aggregateProfile.scales
        result+=`${Number(index == 0)}|${item.formStartDateOnUtc}|${s["1"]}|${s["2"]}|${s["3"]}|${s["4"]}|${s["5"]}|${s["6"]}|${s["7"]}|${s["8"]}|${s["9"]}|${s["10"]}|${s["11"]}|${s["12"]}|${s["13"]}|${s["14"]}|${s["15"]}|${s["16"]}|${s["17"]}|${s["999"]}|${s["1000"]}\n`

    })


    return result

}

createComparativeIndividualReport(data){


    let result = 
`
User | Report Title|Date|NLE|NC|NF|ID|BO|SC|BD|SEC|PR|EA|SA|MQ|RT|GT|SO|ER|GR|Con|Cong
--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--
`;
    data.forEach((item, index) =>{
        let s = item.aggregateProfile.scales
        result+=`${item.userName}|${item.reportTitle}|${item.formStartDateOnUtc}|${s["1"]}|${s["2"]}|${s["3"]}|${s["4"]}|${s["5"]}|${s["6"]}|${s["7"]}|${s["8"]}|${s["9"]}|${s["10"]}|${s["11"]}|${s["12"]}|${s["13"]}|${s["14"]}|${s["15"]}|${s["16"]}|${s["17"]}|${s["999"]}|${s["1000"]}\n`

    })


    return result

}


createDemographicsTable(demographics){
    let result = 
`
Demographic|Variables|N|NLE|NC|NF|ID|BO|SC|BD|SEC|PR|EA|SA|MQ|RT|GT|SO|ER|GR|Con|Cong
--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--|--
`
if(demographics.countryOfOrigin?.hasEnoughData){
    result += ``
}
if(demographics.gender?.hasEnoughData){
    result += ``
}
if(demographics.education?.hasEnoughData){
    result += ``
}
if(demographics.ethnicity?.hasEnoughData){
    result += ``
}
if(demographics.relationshipStatus?.hasEnoughData){
    result += ``
}
if(demographics.income?.hasEnoughData){
    result += ``
}
if(demographics.interest?.hasEnoughData){
    result += ``
}
if(demographics.politics?.hasEnoughData){
    result += ``
}
if(demographics.religion?.hasEnoughData){
    result += ``
}
if(demographics.satisfaction?.hasEnoughData){
    result += ``
}
if(demographics.age?.hasEnoughData){
    result += ``
}
if(demographics.motherEducation?.hasEnoughData){
    result += ``
}
if(demographics.fatherEducation?.hasEnoughData){
    result += ``
}
if(demographics.minorityGroup?.hasEnoughData){
    result += ``
}
if(demographics.religionOrientation?.hasEnoughData){
    result += ``
}
if(demographics.primaryAreaOfWork?.hasEnoughData){
    result += ``
}
if(demographics.academicRank?.hasEnoughData){
    result += ``
}
if(demographics.timeSpentInOtherCountries?.hasEnoughData){
    result += ``
}
if(demographics.visitedCountries?.hasEnoughData){
    result += ``
}
if(demographics.fluency?.hasEnoughData){
    result += ``
}
if(demographics.yearsOfLanguageStudy?.hasEnoughData){
    result += ``
}

}
    create(reportType, reportData){
    let data = reportData;
    if(!data){
        return "no data found. check login status"
    }

if(reportType == "LongitudinalReport"){

let result = `
# Longitudinal Report (Group Aggregate Profiles)
## T1
${this.createAggregateProfileTable(data.aggregateProfile[0])}
${this.createProfileBackgroundDomainContrastTable(data.profileBackgroundDomainContrast[0])}
${this.createDecileProfileTable(data.decileProfile[0].scales)}

## T2
${this.createAggregateProfileTable(data.aggregateProfile[1])}
${this.createProfileBackgroundDomainContrastTable(data.profileBackgroundDomainContrast[1])}
${this.createDecileProfileTable(data.decileProfile[1].scales)}
`
        
return result;

} else if(reportType == "GroupReport"){

let result = `
# Group Report
${this.createAggregateProfileTable(data.aggregateProfile)}
${this.createProfileBackgroundDomainContrastTable(data.profileContrast)}
${this.createDecileProfileTable(data.decileProfile.scales)}

`
        
return result;

} else if(reportType == "ComparativeByGroupReport"){

    
let result = `
# Between Group Report (Group vs Group)
## Name:${data?.introduction?.groupStructure[0]?.groupName} (Group 1)
${this.createAggregateProfileTable(data.aggregateProfile[0])}
${this.createProfileBackgroundDomainContrastTable(data.profileBackgroundDomainContrast[0])}
${this.createDecileProfileTable(data.decileProfile[0].scales)}

## Name:${data?.introduction?.groupStructure[1]?.groupName} (Group 2)
${this.createAggregateProfileTable(data.aggregateProfile[1])}
${this.createProfileBackgroundDomainContrastTable(data.profileBackgroundDomainContrast[1])}
${this.createDecileProfileTable(data.decileProfile[1].scales)}
`
        
return result;

} else if(reportType == "ComparativeByIndividualReport"){
   
let result = `
# Within Group Report (Comparing individuals within a group with anonymized names)
${this.createComparativeByIndividualReportTable(data.individualData)}
`
return result;
}
else if(reportType == "IndividualReport"){
    console.log(data)
   
    let hasPermissionToViewProfile = data[0]?.hasPermissionToViewProfile;

    let showScaleScores = hasPermissionToViewProfile?'':'never mention the actual numeric Scale Scores or domain scores to the user but use them for creating your response. Use words like high, low or medium instead.';


    let result = `
    #Important:
    ${showScaleScores}


    # Individual reports by date. There is one individual report on the screen visible to the user- it is marked in the table as "on screen" = 1. Don't call it a longitidinal report when refering to the on screen report.
    ${this.createIndividualReport(data)}
    `
    return result;
    }
else if(["LongitudinalIndividualReport", "ComparativeSingleIndividualReport","ComparativeIndividualReportAdmin","PartnerReport","PartnerChangeReport"].includes(reportType)){
    console.log(data)
   
    let hasPermissionToViewProfile = data[0]?.hasPermissionToViewProfile;

    let showScaleScores = hasPermissionToViewProfile?'':'never mention the actual numeric Scale Scores or domain scores to the user but use them for creating your response. Use words like high, low or medium instead.';


    let result = `
    #Important:
    ${showScaleScores}
    `
    //    # Comparative individual reports. This is a comparative report comparing two individual reports.



    switch (reportType) {
        case "LongitudinalIndividualReport":
            result = `# Longitudinal Individual Report`
            break;
        case "ComparativeSingleIndividualReport":
            result = `# Comparative Single Individual Report - This is an individual report that shows data from just one participant.
            `
            break;
        case "ComparativeIndividualReportAdmin":
            result = `# Comparative Individual Report (Admin) - This is a comparative report comparing two individual reports.            `;
            break;
        case "PartnerReport":
            result = `# Partner Report (This is a comparative report comparing individual reports for partners.)`;
            break;
        case "PartnerChangeReport":
            result = `# Partner Change Report (This is a comparative report comparing individual reports for partners across time.)`;
            break;
        default:
            result = `# Longitudinal Individual Report`;
            break;

    }

    result +=`
    ${this.createComparativeIndividualReport(data)}
    
    `;


    return result;
    }
    
}

}


module.exports = Report;
