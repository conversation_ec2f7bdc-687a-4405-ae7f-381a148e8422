{"body": "{\n    \"reportType\":\"ComparativeSingleIndividualReport\",\n    \"report\":[\n    {\n        \"formId\": 14206,\n        \"userName\": \"Sample Data 1\",\n        \"formStartDateOnUtc\": \"2020-02-19T22:14:39\",\n        \"negativeLifeEventsIndicator\": 2,\n        \"needsClosureIndicator\": 2,\n        \"toleranceDisequilibriumIndicator\": 2,\n        \"whyWeDoIndicator\": 1,\n        \"accessSelfIndicator\": 2,\n        \"selfAwarenessIndicator\": 1,\n        \"religiousTraditionalimsIndicator\": 2,\n        \"genderTraditionIndicator\": 2,\n        \"socioCulturalIndicator\": 1,\n        \"ecologicalResonanceIndicator\": 1,\n        \"globalEngagementIndicador\": 1,\n        \"hasPermissionToViewProfile\": true,\n        \"hasPermissionToTalkToChatBot\": true,\n        \"hasPermissionToSeeScoresOnChatBot\": true,\n        \"aggregateProfile\": {\n            \"scales\": {\n                \"1\": 53,\n                \"2\": 57,\n                \"3\": 14,\n                \"4\": 50,\n                \"5\": 16,\n                \"6\": 40,\n                \"7\": 81,\n                \"8\": 14,\n                \"9\": 43,\n                \"10\": 38,\n                \"11\": 23,\n                \"12\": 24,\n                \"13\": 46,\n                \"14\": 59,\n                \"15\": 11,\n                \"16\": 21,\n                \"17\": 12,\n                \"999\": 68,\n                \"1000\": 68\n            },\n            \"groupName\": \"Sample Data Program - T1\"\n        },\n        \"demographicsQuestions\": null,\n        \"scalesStronglyAgree\": null,\n        \"scalesStronglyDisagree\": null,\n        \"backgroundStatistic\": {\n            \"fulfillmentOfCoreNeeds\": 14,\n            \"toleranceOfDisequilibrium\": 38,\n            \"criticalThinking\": 19,\n            \"selfAccess\": 30,\n            \"otherAccess\": 35,\n            \"globalAccess\": 16,\n            \"groupName\": \"Sample Data Program - T1\"\n        },\n        \"discusiveAnswer1\": null,\n        \"discusiveAnswer2\": null,\n        \"discusiveAnswer3\": null,\n        \"showExtraData\": false,\n        \"customAgreement\": null,\n        \"reportTitle\": \"Sample Data Program - T1\",\n        \"reportType\": 5,\n        \"isSampleData\": false\n    }\n]\n  }", "resource": "/{proxy+}", "path": "/path/to/resource", "httpMethod": "POST", "isBase64Encoded": true, "queryStringParameters": {"foo": "bar"}, "multiValueQueryStringParameters": {"foo": ["bar"]}, "pathParameters": {"proxy": "/path/to/resource"}, "stageVariables": {"baz": "qux"}, "headers": {"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "Accept-Encoding": "gzip, deflate, sdch", "Accept-Language": "en-US,en;q=0.8", "Cache-Control": "max-age=0", "CloudFront-Forwarded-Proto": "https", "CloudFront-Is-Desktop-Viewer": "true", "CloudFront-Is-Mobile-Viewer": "false", "CloudFront-Is-SmartTV-Viewer": "false", "CloudFront-Is-Tablet-Viewer": "false", "CloudFront-Viewer-Country": "US", "Host": "**********.execute-api.us-east-1.amazonaws.com", "Upgrade-Insecure-Requests": "1", "User-Agent": "Custom User Agent String", "Via": "1.1 08f323deadbeefa7af34d5feb414ce27.cloudfront.net (CloudFront)", "X-Amz-Cf-Id": "cDehVQoZnx43VYQb9j2-nvCh-9z396Uhbp027Y2JvkCPNLmGJHqlaA==", "X-Forwarded-For": "127.0.0.1, *********", "X-Forwarded-Port": "443", "X-Forwarded-Proto": "https"}, "multiValueHeaders": {"Accept": ["text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"], "Accept-Encoding": ["gzip, deflate, sdch"], "Accept-Language": ["en-US,en;q=0.8"], "Cache-Control": ["max-age=0"], "CloudFront-Forwarded-Proto": ["https"], "CloudFront-Is-Desktop-Viewer": ["true"], "CloudFront-Is-Mobile-Viewer": ["false"], "CloudFront-Is-SmartTV-Viewer": ["false"], "CloudFront-Is-Tablet-Viewer": ["false"], "CloudFront-Viewer-Country": ["US"], "Host": ["**********.execute-api.us-east-1.amazonaws.com"], "Upgrade-Insecure-Requests": ["1"], "User-Agent": ["Custom User Agent String"], "Via": ["1.1 08f323deadbeefa7af34d5feb414ce27.cloudfront.net (CloudFront)"], "X-Amz-Cf-Id": ["cDehVQoZnx43VYQb9j2-nvCh-9z396Uhbp027Y2JvkCPNLmGJHqlaA=="], "X-Forwarded-For": ["127.0.0.1, *********"], "X-Forwarded-Port": ["443"], "X-Forwarded-Proto": ["https"]}, "requestContext": {"accountId": "**********12", "resourceId": "123456", "stage": "prod", "requestId": "c6af9ac6-7b61-11e6-9a41-93e8<PERSON><PERSON><PERSON>f", "requestTime": "09/Apr/2015:12:34:56 +0000", "requestTimeEpoch": *************, "identity": {"cognitoIdentityPoolId": null, "accountId": null, "cognitoIdentityId": null, "caller": null, "accessKey": null, "sourceIp": "127.0.0.1", "cognitoAuthenticationType": null, "cognitoAuthenticationProvider": null, "userArn": null, "userAgent": "Custom User Agent String", "user": null}, "path": "/prod/path/to/resource", "resourcePath": "/{proxy+}", "httpMethod": "POST", "apiId": "**********", "protocol": "HTTP/1.1"}}