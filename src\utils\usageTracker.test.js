const UsageTracker = require('./usageTracker');

// Simple test function
function testUsageTracker() {
    console.log('Testing UsageTracker...');
    
    const tracker = new UsageTracker();
    
    // Test 1: Basic usage logging
    console.log('\n1. Testing basic usage logging:');
    tracker.logUsage('gpt-4o-mini', 100, 50);
    console.log('Usage stats:', JSON.stringify(tracker.getUsageStats(), null, 2));
    console.log('Total cost:', tracker.getTotalCost());
    
    // Test 2: Model pricing
    console.log('\n2. Testing model pricing:');
    const gpt4Pricing = tracker.getModelPricing('gpt-4');
    const gpt4oPricing = tracker.getModelPricing('gpt-4o-2024-08-06');
    const unknownPricing = tracker.getModelPricing('unknown-model');
    
    console.log('GPT-4 pricing:', gpt4Pricing);
    console.log('GPT-4o pricing:', gpt4oPricing);
    console.log('Unknown model pricing (default):', unknownPricing);
    
    // Test 3: Cost calculation
    console.log('\n3. Testing cost calculation:');
    const cost1 = tracker.calculateCost('gpt-4o-mini', 1000, 500);
    const cost2 = tracker.calculateCost('gpt-4', 1000, 500);
    
    console.log('Cost for 1000 input + 500 output tokens with gpt-4o-mini:', cost1);
    console.log('Cost for 1000 input + 500 output tokens with gpt-4:', cost2);
    
    // Test 4: Usage from response object
    console.log('\n4. Testing usage from response object:');
    const mockResponse = {
        usage: {
            prompt_tokens: 200,
            completion_tokens: 100
        }
    };
    
    tracker.logUsageFromResponse('gpt-3.5-turbo-1106', mockResponse);
    
    // Test 5: Usage summary
    console.log('\n5. Testing usage summary:');
    const summary = tracker.getUsageSummary();
    console.log('Usage summary:', JSON.stringify(summary, null, 2));
    
    // Test 6: Clear stats
    console.log('\n6. Testing clear stats:');
    console.log('Stats before clear:', tracker.getUsageStats().length);
    tracker.clearStats();
    console.log('Stats after clear:', tracker.getUsageStats().length);
    
    console.log('\nUsageTracker tests completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
    testUsageTracker();
}

module.exports = { testUsageTracker };
