/*

 We have 4 services, let me know if you need any other info.

    POST:: api/beingbevi/UseInteractionIndividualReport

{
  "ReportId": 14141,
  "NumToken": 15,
  "ReportModel": "gpt-3",
  "OutputToken": 51,
  "InputToken": 16,
  "DolarSpend": 150.4
}

    POST: api/beingbevi/UseInteractionOtherReports

{
  "ReportId": 14141,
  "NumToken": 15,
  "ReportModel": "gpt-3",
  "OutputToken": 51,
  "InputToken": 16,
  "DolarSpend": 150.4
}

    GET: api/beingbevi/GetAvailableInteractionsForIndividualReport?reportId=
    GET: api/beingbevi/GetAvailableInteractionsForOtherReports?reportId=

Both methods return:
{
    "availableInteractions": 18
}


Report Id, report type, token, demo (check the canTalkToChatBot capability), hideScores (check the canVisualizeProfileIndividualReport capability), locale
{
  "ReportId": 128,
  "Statistics": [
    {
        "NumToken": 15,
        "ReportModel": "gpt-3",
        "OutputToken": 51,
        "InputToken": 16,
        "DolarSpend": 150.4
    },
    {
        "NumToken": 17,
        "ReportModel": "gpt-4",
        "OutputToken": 510,
        "InputToken": 160,
        "DolarSpend": 80.4
    }
  ]
 
}
*/

const axios = require('axios');



async function useInteraction(reportAPI, statsArray){
    try {
        const headers = {
            Authorization: `Bearer ${reportAPI.token}`,
        }; 
        if(reportAPI.apiPrefix !== 'dev' && reportAPI.apiPrefix !== 'apiprod'){
            return
        }
        let endpoint = "UseInteractionOtherReports"
        if(reportAPI.type === "IndividualReport"){
            endpoint = "UseInteractionIndividualReport";
        }
        let body = {
            
                "ReportId": reportAPI.id,
                "Statistics": statsArray
               
              
        }
        const response = await axios.post(`https://${reportAPI.apiPrefix}.takethebevi.com/api/beingbevi/${endpoint}`,body, { headers });
        // console.log(response.data);
        return response.data;
        } catch (error) {
        //   console.error(error);
          return error;
        }
}


async function getAvailableInteractions(reportAPI){
    let id = reportAPI.id.split(",").map((id)=>{return parseInt(id)})[0]
    try {
        const headers = {
            Authorization: `Bearer ${reportAPI.token}`,
        }; 

        let endpoint = "GetAvailableInteractionsForOtherReports"
        if(reportAPI.type === "IndividualReport"){
            endpoint = "GetAvailableInteractionsForIndividualReport";
        }
        const response = await axios.get(`https://${reportAPI.apiPrefix}.takethebevi.com/api/beingbevi/${endpoint}?reportId=${id}`, { headers });
        return response.data.availableInteractions;

    } catch (error) {
        throw error;
        return error;
    }
}


module.exports = {
    useInteraction,
    getAvailableInteractions
};

