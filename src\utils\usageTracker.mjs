/**
 * Usage tracking utility for OpenAI API calls (ES Module version)
 * Handles token counting, cost calculation, and usage statistics
 */
export class UsageTracker {
    constructor() {
        this.usageStats = [];
        this.modelPricing = {
            // Current GPT-4.1 models (2025)
            'gpt-4.1': { input: 2.00/1000, output: 8.00/1000 },
            'gpt-4.1-2025-04-14': { input: 2.00/1000, output: 8.00/1000 },
            'gpt-4.1-mini': { input: 0.40/1000, output: 1.60/1000 },
            'gpt-4.1-nano': { input: 0.10/1000, output: 0.40/1000 },

            // Current GPT-4o models (2024-2025)
            'gpt-4o': { input: 5.00/1000, output: 20.00/1000 },
            'gpt-4o-2024-08-06': { input: 5.00/1000, output: 20.00/1000 },
            'gpt-4o-2024-05-13': { input: 5.00/1000, output: 20.00/1000 },
            'gpt-4o-2024-11-20': { input: 5.00/1000, output: 20.00/1000 },
            'gpt-4o-mini': { input: 0.60/1000, output: 2.40/1000 },
            'gpt-4o-mini-2024-07-18': { input: 0.60/1000, output: 2.40/1000 },

            // Reasoning models
            'o3': { input: 2.00/1000, output: 8.00/1000 },
            'o4-mini': { input: 1.10/1000, output: 4.40/1000 },
            'o1': { input: 15.00/1000, output: 60.00/1000 },
            'o1-pro': { input: 60.00/1000, output: 240.00/1000 },

            // Legacy models (deprecated but may still be in use)
            'gpt-4-1106-preview': { input: 0.01/1000, output: 0.03/1000 },
            'gpt-3.5-turbo-1106': { input: 0.001/1000, output: 0.002/1000 },
            'gpt-4': { input: 0.03/1000, output: 0.06/1000 },
            'gpt-3.5-turbo': { input: 0.001/1000, output: 0.002/1000 },

            // Default pricing for unknown models (using gpt-4o-mini as baseline)
            'default': { input: 0.60/1000, output: 2.40/1000 }
        };
    }

    /**
     * Get pricing for a specific model
     * @param {string} model - The model name
     * @returns {object} - Object with input and output pricing per token
     */
    getModelPricing(model) {
        // Check for exact matches first
        if (this.modelPricing[model]) {
            return this.modelPricing[model];
        }

        // Check for partial matches
        for (const [modelKey, pricing] of Object.entries(this.modelPricing)) {
            if (model.includes(modelKey)) {
                return pricing;
            }
        }

        // Return default pricing if no match found
        return this.modelPricing.default;
    }

    /**
     * Calculate cost for token usage
     * @param {string} model - The model used
     * @param {number} inputTokens - Number of input tokens
     * @param {number} outputTokens - Number of output tokens
     * @returns {number} - Cost in USD
     */
    calculateCost(model, inputTokens, outputTokens) {
        const pricing = this.getModelPricing(model);
        return (inputTokens * pricing.input) + (outputTokens * pricing.output);
    }

    /**
     * Log usage statistics
     * @param {string} model - The model used
     * @param {number} inputTokens - Number of input tokens
     * @param {number} outputTokens - Number of output tokens
     * @param {object} additionalData - Any additional data to include
     */
    async logUsage(model, inputTokens, outputTokens, additionalData = {}) {
        console.log("inputTokens", inputTokens);
        console.log("outputTokens", outputTokens);
        console.log("model", model);

        const cost = this.calculateCost(model, inputTokens, outputTokens);
        console.log('Price USD: ', cost);

        const usageEntry = {
            "NumToken": (outputTokens + inputTokens),
            "ReportModel": model,
            "OutputToken": outputTokens,
            "InputToken": inputTokens,
            "DolarSpend": cost,
            "timestamp": new Date().toISOString(),
            ...additionalData
        };

        this.usageStats.push(usageEntry);
        return usageEntry;
    }

    /**
     * Log usage from OpenAI response object
     * @param {string} model - The model used
     * @param {object} response - OpenAI response object with usage property
     * @param {object} additionalData - Any additional data to include
     */
    async logUsageFromResponse(model, response, additionalData = {}) {
        const usage = response.usage || {};
        const inputTokens = usage.prompt_tokens || usage.input_tokens || 0;
        const outputTokens = usage.completion_tokens || usage.output_tokens || 0;
        
        return await this.logUsage(model, inputTokens, outputTokens, additionalData);
    }

    /**
     * Get current usage statistics
     * @returns {array} - Array of usage entries
     */
    getUsageStats() {
        return this.usageStats;
    }

    /**
     * Get total cost from all logged usage
     * @returns {number} - Total cost in USD
     */
    getTotalCost() {
        return this.usageStats.reduce((sum, item) => sum + Number(item.DolarSpend), 0);
    }

    /**
     * Clear usage statistics
     */
    clearStats() {
        this.usageStats = [];
    }

    /**
     * Get usage summary
     * @returns {object} - Summary of usage statistics
     */
    getUsageSummary() {
        const totalTokens = this.usageStats.reduce((sum, item) => sum + item.NumToken, 0);
        const totalInputTokens = this.usageStats.reduce((sum, item) => sum + item.InputToken, 0);
        const totalOutputTokens = this.usageStats.reduce((sum, item) => sum + item.OutputToken, 0);
        const totalCost = this.getTotalCost();

        return {
            totalRequests: this.usageStats.length,
            totalTokens,
            totalInputTokens,
            totalOutputTokens,
            totalCost,
            averageCostPerRequest: this.usageStats.length > 0 ? totalCost / this.usageStats.length : 0,
            usageStats: this.usageStats
        };
    }
}

export default UsageTracker;
