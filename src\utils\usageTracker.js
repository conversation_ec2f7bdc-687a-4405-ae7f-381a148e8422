const { GPTTokens } = require('gpt-tokens');

/**
 * Usage tracking utility for OpenAI API calls
 * Handles token counting, cost calculation, and usage statistics
 */
class UsageTracker {
    constructor() {
        this.usageStats = [];
        this.modelPricing = {
            // Current GPT-4.1 models (2025) - pricing per token (from OpenAI pricing page / 1M)
            'gpt-4.1': { input: 2.00/1000000, output: 8.00/1000000 },
            'gpt-4.1-2025-04-14': { input: 2.00/1000000, output: 8.00/1000000 },
            'gpt-4.1-mini': { input: 0.40/1000000, output: 1.60/1000000 },
            'gpt-4.1-nano': { input: 0.100/1000000, output: 0.400/1000000 },

            // Current GPT-4o models (2024-2025) - pricing per token (from OpenAI pricing page / 1M)
            'gpt-4o': { input: 5.00/1000000, output: 20.00/1000000 },
            'gpt-4o-2024-08-06': { input: 5.00/1000000, output: 20.00/1000000 },
            'gpt-4o-2024-05-13': { input: 5.00/1000000, output: 20.00/1000000 },
            'gpt-4o-2024-11-20': { input: 5.00/1000000, output: 20.00/1000000 },
            'gpt-4o-mini': { input: 0.15/1000000, output: 0.60/1000000 },
            'gpt-4o-mini-2024-07-18': { input: 0.15/1000000, output: 0.60/1000000 },

            // Reasoning models - pricing per token (from OpenAI pricing page / 1M)
            'o3': { input: 2.00/1000000, output: 8.00/1000000 },
            'o4-mini': { input: 1.100/1000000, output: 4.400/1000000 },
            'o1': { input: 15.00/1000000, output: 60.00/1000000 },
            'o1-pro': { input: 60.00/1000000, output: 240.00/1000000 },

            // Legacy models (deprecated but may still be in use) - estimated pricing per token
            'gpt-4-1106-preview': { input: 10.00/1000000, output: 30.00/1000000 },
            'gpt-3.5-turbo-1106': { input: 1.00/1000000, output: 2.00/1000000 },
            'gpt-4': { input: 30.00/1000000, output: 60.00/1000000 },
            'gpt-3.5-turbo': { input: 1.00/1000000, output: 2.00/1000000 },

            // Default pricing for unknown models (using gpt-4o-mini as baseline)
            'default': { input: 0.15/1000000, output: 0.60/1000000 }
        };
    }

    /**
     * Get pricing for a specific model
     * @param {string} model - The model name
     * @returns {object} - Object with input and output pricing per token
     */
    getModelPricing(model) {
        // Check for exact matches first
        if (this.modelPricing[model]) {
            return this.modelPricing[model];
        }

        // Check for partial matches
        for (const [modelKey, pricing] of Object.entries(this.modelPricing)) {
            if (model.includes(modelKey)) {
                return pricing;
            }
        }

        // Return default pricing if no match found
        return this.modelPricing.default;
    }

    /**
     * Calculate cost for token usage
     * @param {string} model - The model used
     * @param {number} inputTokens - Number of input tokens
     * @param {number} outputTokens - Number of output tokens
     * @returns {number} - Cost in USD
     */
    calculateCost(model, inputTokens, outputTokens) {
        const pricing = this.getModelPricing(model);
        return (inputTokens * pricing.input) + (outputTokens * pricing.output);
    }

    /**
     * Log usage statistics
     * @param {string} model - The model used
     * @param {number} inputTokens - Number of input tokens
     * @param {number} outputTokens - Number of output tokens
     * @param {object} additionalData - Any additional data to include
     */
    async logUsage(model, inputTokens, outputTokens, additionalData = {}) {
        console.log("inputTokens", inputTokens);
        console.log("outputTokens", outputTokens);
        console.log("model", model);

        const cost = this.calculateCost(model, inputTokens, outputTokens);
        console.log('Price USD: ', cost);

        const usageEntry = {
            "NumToken": (outputTokens + inputTokens),
            "ReportModel": model,
            "OutputToken": outputTokens,
            "InputToken": inputTokens,
            "DolarSpend": cost,
            "timestamp": new Date().toISOString(),
            ...additionalData
        };

        this.usageStats.push(usageEntry);
        return usageEntry;
    }

    /**
     * Calculate token usage from messages using GPTTokens library
     * @param {string} model - The model name
     * @param {array} messages - Array of message objects
     * @returns {object} - Object with promptUsedTokens and completionUsedTokens
     */
    calculateTokensFromMessages(model, messages) {
        try {
            const options = {
                model: model,
                messages: messages
            };
            console.log("Calculating tokens for:", JSON.stringify(options));
            
            const usageInfo = new GPTTokens(options);
            console.log("Token calculation result:", JSON.stringify(usageInfo));
            
            return {
                promptUsedTokens: usageInfo.promptUsedTokens,
                completionUsedTokens: usageInfo.completionUsedTokens,
                totalTokens: usageInfo.usedTokens
            };
        } catch (error) {
            console.error("Error calculating tokens:", error);
            return {
                promptUsedTokens: 0,
                completionUsedTokens: 0,
                totalTokens: 0
            };
        }
    }

    /**
     * Log usage from OpenAI response object
     * @param {string} model - The model used
     * @param {object} response - OpenAI response object with usage property
     * @param {object} additionalData - Any additional data to include
     */
    async logUsageFromResponse(model, response, additionalData = {}) {
        const usage = response.usage || {};
        const inputTokens = usage.prompt_tokens || 0;
        const outputTokens = usage.completion_tokens || 0;
        
        return await this.logUsage(model, inputTokens, outputTokens, additionalData);
    }

    /**
     * Get current usage statistics
     * @returns {array} - Array of usage entries
     */
    getUsageStats() {
        return this.usageStats;
    }

    /**
     * Get total cost from all logged usage
     * @returns {number} - Total cost in USD
     */
    getTotalCost() {
        return this.usageStats.reduce((sum, item) => sum + Number(item.DolarSpend), 0);
    }

    /**
     * Clear usage statistics
     */
    clearStats() {
        this.usageStats = [];
    }

    /**
     * Get usage summary
     * @returns {object} - Summary of usage statistics
     */
    getUsageSummary() {
        const totalTokens = this.usageStats.reduce((sum, item) => sum + item.NumToken, 0);
        const totalInputTokens = this.usageStats.reduce((sum, item) => sum + item.InputToken, 0);
        const totalOutputTokens = this.usageStats.reduce((sum, item) => sum + item.OutputToken, 0);
        const totalCost = this.getTotalCost();

        return {
            totalRequests: this.usageStats.length,
            totalTokens,
            totalInputTokens,
            totalOutputTokens,
            totalCost,
            averageCostPerRequest: this.usageStats.length > 0 ? totalCost / this.usageStats.length : 0,
            usageStats: this.usageStats
        };
    }
}

module.exports = UsageTracker;
