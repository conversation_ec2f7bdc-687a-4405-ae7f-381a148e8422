# This is the SAM template that represents the architecture of your serverless application
# https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-template-basics.html

# The AWSTemplateFormatVersion identifies the capabilities of the template
# https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/format-version-structure.html
AWSTemplateFormatVersion: 2010-09-09
Description: >-
  beingbevi-gpt

# Transform section specifies one or more macros that AWS CloudFormation uses to process your template
# https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/transform-section-structure.html
Transform:
- AWS::Serverless-2016-10-31

# Resources declares the AWS resources that you want to include in the stack
# https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/resources-section-structure.html
Conditions:
  IsProd: !Equals [ !Ref Environment, prod ]

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues:
      - dev
      - prod
      

  InsightsModel:
    Type: String
    Default: gpt-4.1-2025-04-14
    Description: "OpenAI model to use"
  SecretKey:
    Type: String
    Default: bevisecretkey
    Description: "Secret key for API access"

  

Resources:
  # Each Lambda function is defined by properties:
  # https://github.com/awslabs/serverless-application-model/blob/master/versions/2016-10-31.md#awsserverlessfunction

  # This is a Lambda function config associated with the source code: hello-from-lambda.js
  BeingBeviLambdaFunction:
  
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/index.handler
      Runtime: nodejs20.x
      Architectures:
        - x86_64
      MemorySize: !If [IsProd, 256, 256]
      Timeout: !If [IsProd, 300, 300]
      Description: A Lambda function that returns a static string.
      Policies:
        # Give Lambda basic execution Permission to the helloFromLambda
        - AWSLambdaBasicExecutionRole
      FunctionUrlConfig:
        AuthType: NONE
        InvokeMode: RESPONSE_STREAM
        Cors:
          AllowCredentials: False
          AllowHeaders: 
            - "Content-Type"
          AllowMethods: 
            - "POST"
          AllowOrigins: 
            - '*'
      Environment:
        Variables:
          OPENAI_API_KEY: ""
         
    
          
  TokenLambdaFunction:
  
    Type: AWS::Serverless::Function
    Properties:
      Handler: src/handlers/token.handler
      Runtime: nodejs20.x
      Architectures:
        - x86_64
      MemorySize: !If [IsProd, 256, 256]
      Timeout: !If [IsProd, 300, 300]
      Description: A Lambda function that returns a static string.
      Policies:
        # Give Lambda basic execution Permission to the helloFromLambda
        - AWSLambdaBasicExecutionRole
      FunctionUrlConfig:
        AuthType: NONE
        InvokeMode: BUFFERED
        Cors:
          AllowCredentials: False
          AllowHeaders: 
            - "Content-Type"
          AllowMethods: 
            - "GET"
          AllowOrigins: 
            - '*'
     
  InsightsLambdaFunction:
    
      Type: AWS::Serverless::Function
      Properties:
        Handler: src/handlers/insights.handler
        Runtime: nodejs20.x
        Architectures:
          - x86_64
        MemorySize: !If [IsProd, 256, 256]
        Timeout: !If [IsProd, 300, 300]
        Description: A Lambda function that takes in a bevi report and returns an insights report.
        Policies:
          # Give Lambda basic execution Permission to the helloFromLambda
          - AWSLambdaBasicExecutionRole
        FunctionUrlConfig:
          AuthType: NONE
          InvokeMode: BUFFERED
          Cors:
            AllowCredentials: False
            AllowHeaders: 
              - "Content-Type"
              - "x-api-key"
            AllowMethods: 
              - "GET"
              - "POST"
            AllowOrigins: 
              - '*'
        Environment:
          Variables:
            OPENAI_API_KEY: ""
            MODEL: !Ref InsightsModel
            SECRET_KEY: !Ref SecretKey
     
Outputs:
  

  MyFunctionUrlEndpoint:
      Description: "My Lambda Function URL Endpoint"
      Value:
        Fn::GetAtt: BeingBeviLambdaFunctionUrl.FunctionUrl
  TokenUrlEndpoint:
      Description: "My Lambda Function URL Endpoint"
      Value:
        Fn::GetAtt: TokenLambdaFunctionUrl.FunctionUrl
  InsightsUrlEndpoint:
      Description: "My Lambda Function URL Endpoint"
      Value:
        Fn::GetAtt: InsightsLambdaFunctionUrl.FunctionUrl
        

