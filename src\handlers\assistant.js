const axios = require('axios');
const https = require('https');  // Importing https module

const jwt_decode = require("jwt-decode");

const OpenAI = require("openai");


var decodedToken


const api_key = process.env.API_KEY || "***************************************************";
  
const openai = new OpenAI({
    apiKey: api_key,
  //   organization:"org-vbpJSKWxjY8NThdSh35X5Qty"
  });




const assistant_id = "asst_ob7p0TBJPdfOlJKym2wKcgfq";

exports.handler = async (req, res, context) => {
       


        console.log(req);
        let jsonData = {}
        decodedToken = {}



        res.write("/nlog:::request recieved "+'/n/n');
        if(req.body === 'init'){
           
              res.end();
              return;
        }else{
            jsonData = JSON.parse(req.body);

        }
        let threadId = jsonData.threadId
        if(threadId){
            //get existing thread
            try{
                const thread = await openai.beta.threads.retrieve(threadId)
            }catch(err){
                //create new thread if not found
                const emptyThread = await openai.beta.threads.create();
                threadId = emptyThread.id;
            }
            res.write("/nlog:::retrieved existing thread with id: " + threadId);
        }else{
            //create new thread
            const emptyThread = await openai.beta.threads.create();
            threadId = emptyThread.id;
            res.write("/nlog:::created new thread with id: " + threadId);


            let command = JSON.stringify({
                function:"setThreadId", arguments:[threadId]
            }
            )
            res.write("/nsystem_command:::"+command );
        }
        res.write("/nlog:::processing messages "+'/n/n');
        let history = jsonData['messages']

        res.write("/nlog:::running run "+'/n/n');
        console.log(JSON.stringify(jsonData['messages']))

        const threadMessages = await openai.beta.threads.messages.create(
            threadId,
            history.pop()
        );

        const run = await openai.beta.threads.runs.create(
            threadId,
            { assistant_id: assistant_id }
        );
        // const run = await openai.beta.threads.createAndRun({
        //     assistant_id: assistant_id,
        //     thread: {
        //       messages: history
        //     },
        // });
        let runId = run.id;
        res.write("/nlog:::runId" + runId);


        // Get run status
       
        //periodically check run status
        async function checkStatus(){
            const run_status = await openai.beta.threads.runs.retrieve(
                run.thread_id,
                run.id
            );
            res.write("/nlog:::status " + run_status.status);
            if(run_status.status === 'completed'){
                res.write("/nlog:::getting messages for " + run.thread_id);
                const messages = await openai.beta.threads.messages.list(
                    run.thread_id
                );
                // res.write("/nlog:::messages obs " + JSON.stringify(messages.body));
                let assistantMessages = []
                for(let i = 0; i <= messages.body.data.length-1; i++){
                    let message = messages.body.data[i];
                    res.write("/nlog:::message " + JSON.stringify(message));
                    if(message.role !== 'assistant'){
                        break;
                    }
                    // res.write("/nlog:::message " + JSON.stringify(message));

                    // res.write("/nlog:::message "+JSON.stringify(message.content[0].text.value));
                    res.write("/n{{"+message.content[0].text.value);
                }
                res.write("/nlog:::done " + run_status.status);

                // res.write(assistantMessages.join(''));
    
               
            }else{
                await new Promise(resolve => setTimeout(resolve, 1000));
                return checkStatus();
            }
        }
        await checkStatus();
        // await openai.beta.threads.del(run.thread_id);
        res.write("/nlog:::done and end");
        res.end();

    
};

