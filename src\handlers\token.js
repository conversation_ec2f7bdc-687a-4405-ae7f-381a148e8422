const axios = require('axios');

exports.handler = async (event, context) => {
    const speechRegion = 'eastus';
    const speechKey = '817741e2b52a48b5871ea3703d0bcbf6';

    try {
        const tokenResponse = await axios.post(`https://${speechRegion}.api.cognitive.microsoft.com/sts/v1.0/issueToken`, null, {
            headers: {
                'Ocp-Apim-Subscription-Key': speechKey,
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });

        return {
            statusCode: 200,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                token: tokenResponse.data,
                region: speechRegion
            })
        };
    } catch (err) {
        console.error(err.message);
        return {
            statusCode: 401,
            body: JSON.stringify({
                message: "There was an error authorizing your speech key."
            })
        };
    }
};
