const OpenAI = require("openai");

const api_key = process.env.API_KEY || "***************************************************";

  const openai = new OpenAI({
    apiKey: api_key,
  });


const generateSuggestedUtterances = async (messages)=>{
    let prompt = 
`${JSON.stringify(messages)}`;
    const response =  await openai.chat.completions.create({
        model: "gpt-3.5-turbo-0613",
        temperature:0.7,
        messages: [{"role":"system", "content":prompt}],
        functions:[
            {
                "name": "suggest_utterances",
                "description": "send 3 short, suggested utterances generated from the text",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "a": {
                            "type": "string"
                        },
                        "b": {
                            "type": "string"
                        },
                        "c": {
                            "type": "string"
                        }

                    },
                    "required": ["a", "b", "c"],
                },
            }
        ],
        function_call:{"name": "suggest_utterances"}
    });
}

    
// const generateSuggestedUtterances = async (messages)=>{
//     let prompt = 
// `${JSON.stringify(messages)}`;
//     const response = await openai.createChatCompletion({
//         model: "gpt-3.5-turbo-0613",
//         temperature:0.7,
//         messages: [{"role":"system", "content":prompt}],
//         functions:[
//             {
//                 "name": "suggest_utterances",
//                 "description": "send 3 short, suggested utterances generated from the text",
//                 "parameters": {
//                     "type": "object",
//                     "properties": {
//                         "a": {
//                             "type": "string"
//                         },
//                         "b": {
//                             "type": "string"
//                         },
//                         "c": {
//                             "type": "string"
//                         }

//                     },
//                     "required": ["a", "b", "c"],
//                 },
//             }
//         ],
//         function_call:{"name": "suggest_utterances"}
//     });

//     // return JSON.parse(result.data.choices[0].message.content)
    
//     if(response.data["choices"][0]["message"]['function_call']){
//         let result = response.data["choices"][0]["message"]['function_call']
//         return result['arguments']
//     }else{
//         return;
//     }
   
// }

async function main(){
    let utterances = await generateSuggestedUtterances(
        [{ role: 'user', content: 'Who are you?' },
        {
          role: 'assistant',
          content: "{{ I'm Being Bevi, your guide. }}{{ How can I help you explore your EI Self or answer any questions you have about the Beliefs, Events, and Values Inventory (BEVI)? }} // How is Bevi used?|What are scale scores?| "
        },
        { role: 'user', content: 'what does the bevi measure?' },
        {
          role: 'assistant',
          content: "{{The Beliefs, Events, and Values Inventory (BEVI) is a psychological assessment tool that measures an individual or group's beliefs, values, life events, and how they interact to shape one's understanding and experiences.}}{{ The BEVI focuses on various aspects their life, such as core needs fulfillment, tolerance for ambiguity, critical thinking, self and other recognition, and global connectedness.}}{{ By evaluating these factors, the BEVI helps in understanding their worldview, self-awareness, and openness to different perspectives. }} // How is BEVI used?|What are the different scales in BEVI?|explain my BEVI scores?|"
        }
      ]
    )
    console.log(utterances)
}
main()



// const Report = require('./Report');



// async function fetchData(reportAPI) {
//     // let customToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.4wIyF5pSY0oFMb9h0IQI2QgAd67OX3N2cDspqQ_bvGQ";

//     // console.log(reportAPI)
//     // console.log(`https://${reportAPI.apiPrefix}.takethebevi.com/api/beingbevi/${reportAPI.type}?reportId=${reportAPI.id}`)


//     try {
//     const headers = {
//         Authorization: `Bearer ${reportAPI.token}`,
//     }; 
//     if(reportAPI.apiPrefix !== 'dev' && reportAPI.apiPrefix !== 'apiprod'){
//         return
//     }
//         const response = await axios.get(`https://${reportAPI.apiPrefix}.takethebevi.com/api/beingbevi/${reportAPI.type}?reportId=${reportAPI.id}`, { headers });
//     // console.log(response.data);
//     return response.data;
//     } catch (error) {
//     //   console.error(error);
//       return error;
//     }
//   }
  

  
// let report = new Report()
// report.create("IndividualReport")

// console.log(new r)

/*

request recieved with prompt

getReport - returns report


 */