const axios = require('axios');
const https = require('https');  // Importing https module

const jwt_decode = require("jwt-decode");
const { getBalance, useBalance, addBalance } = require('./budget');
const { useInteraction, getAvailableInteractions } = require('./interactionBudget');

const OpenAI = require("openai");

const Report = require('./Report');
const UsageTracker = require('../utils/usageTracker');

// const gpt4model = "gpt-4";
const gpt4model = "gpt-4o-2024-08-06";
// const gpt4model = "mistralai/Mixtral-8x7B-Instruct-v0.1";
const gpt3model = "gpt-4o-mini"; 
// const gpt3model = "mistralai/Mixtral-8x7B-Instruct-v0.1"; 
// const gpt4model = "gpt-3.5-turbo-1106"; 
// const gpt3model = "gpt-4-1106-preview"; 

let balance = 0;
let jsonData
let usageTracker = new UsageTracker();

var decodedToken

const glossary = `
BEVI Domains->Scales with Descriptions for High, Medium, and Low 
7 domains containing 17 scales amongst them.
1.Validity Scales: Consistency, Congruency.
2.Formative Variables:
- Negative Life Events (NLE): Impact of life experiences on beliefs/values. High: Trauma; Medium: Life difficulties; Low: Positive experiences.
- Fulfillment of Core Needs: Openness to experiences, caring for others. High: Embracing experiences and needs; Medium: Moderately open; Low: Less open.
- Needs Closure (NC): Impact of adversity on core needs. High: Significant impact; Medium: Some impact; Low: Minimal impact.
- Needs Fulfillment (NF): Personal openness, past influences. High: Open to feelings/influences; Medium: Somewhat open; Low: Closed off.
- Identity Diffusion (ID): Clarity of personal identity. High: Confused about self; Medium: Somewhat unclear; Low: Clear purpose.
3.Tolerance of Disequilibrium:
- Basic Openness (BO): Acceptance of emotions/thoughts. High: Very open; Medium: Moderately open; Low: Guarded.
- Self Certitude (SC): Approach to life challenges. High: Highly resilient, persistent, and less tolerant of excuses, but may lack compassion for self and others' vulnerabilities; Medium: Balanced approach combining resilience with empathy; Low: Embraces vulnerability and resilience, yet sometimes wishes for more assertiveness in overcoming obstacles.
4.Critical Thinking:
- Basic Determinism (BD): Causality/worldview. High: Binary thinking; Medium: Some nuance; Low: Avoids oversimplification.
- Socioemotional Convergence (SEC): Complexity handling. High: Sees multiple perspectives; Medium: Some complexity; Low: Prefers simplicity.
5.Self-Access:
- Physical Resonance (PR): Comfort with physical needs/nature. High: At ease; Medium: Somewhat comfortable; Low: Reserved.
- Emotional Attunement (EA): Recognition of emotions. High: Deep awareness; Medium: Some awareness; Low: Overwhelmed by emotions.
- Self Awareness (SA): Understanding self-origin. High: Introspective; Medium: Some self-reflection; Low: Less focused on reflection.
- Meaning Quest (MQ): Pursuit of life's purpose. High: Actively seeking; Medium: Some pursuit; Low: Less focused on meaning.
6.Other Access:
- Religious Traditionalism (RT): Reliance on religious/spiritual beliefs. High: Strongly guided by faith; Medium: Some comfort with faith; Low: Skeptical.
- Gender Traditionalism (GT): Adherence to traditional gender roles. High: Rigid views; Medium: Somewhat traditional; Low: Flexible views.
- Sociocultural Openness (SO): Receptivity to diversity. High: Values diversity; Medium: Somewhat open; Low: Less concerned with diversity.
7.Global Access:
- Ecological Resonance (ER): Environmental concern. High: Environmentally conscious; Medium: Some concern; Low: Less concerned.
- Global Resonance (GR): Interest in global issues. High: Globally aware and active; Medium: Some global interest; Low: More practical, less global focus.
### Glossary:
- BEVI (Beliefs, Events and Values Inventory): Belief/events/value/life event profiler. (more info. thebevi.com) - created by Dr. Craig Shealy
- Negative Life Events: Shape beliefs/values.
- EI Self: Belief/value internalization model: Endoself, Mesoself, Ectoself, Exoself.
- Equilintegration (EI) or EI Theory: Emotional/cognitive balance/integration. Explains how beliefs, values, and worldviews are formed, maintained, and changed, focusing on their acquisition, resistance to alteration, and circumstances leading to modification.
- Continuum of Beliefs: How belief commitments range from very strong to weak or ambivalent 
- Endoself: Core self w/ Adaptive Potential, Core Needs. Evolved from past. The self is always trying to get its needs met throughout life; the environment at the “Exoself” level may or may not be aligned with the core self getting its needs met and pursuing its potential
- Mesoself: Links Core Self & Ectoself.  Acts as a mediator between potential beliefs that might be internalized to the degree to which core needs and potential might be met – or could be threatened -- particularly given prevailing formative variables, which may or may not be aligned with one’s core needs and potential 
- Ectoself: Houses beliefs, values, "schemattitudes". Forms Worldview. Houses 17 BEVI scales.
- Exoself: Human-world interaction system, influenced by Formative Variables. Identity from Formative Variables & Core Needs interaction.
Formative Variables: factors influencing our beliefs and values, shaped by societal, cultural, community, or familial contexts. They include culture, gender, religion, education, and economic background. These variables may align or misalign with our core needs and adaptive potential. Misalignment can lead to internalizing beliefs and values that hinder fulfilling our core needs and potential.
- Profile Contrast: Compares Full Scale scores of group's high, middle, and low thirds, visualizing variations in identity structures and their influence on perceptions and world interactions. Uses the Full Scale Score on the BEVI to do so, divided into the top, middle, and bottom third of respondents in any group-based administration

Core needs in BEVI's EI framework:
1. Appetitive: Basic physical survival needs.
2. Attachment: Need for connection and security.
3. Affective: Need for emotional expression and acceptance.
4. Acknowledgement: Need for recognition and validation.
5. Activation: Need for stimulation and action.
6. Affiliative: Need for social belonging and cooperation.
7. Actualizing: Need for personal growth and fulfillment.
8. Attunement: Need for internal and external harmony.
9. Awareness: Need for understanding and consciousness.
`

const generateSuggestedUtterances = async (messages)=>{
    let model = gpt3model
    let prompt = 
`${JSON.stringify(messages)}`;
    const response = await openai.chat.completions.create({
        model: model,
        temperature:0.7,
        messages: [{"role":"system", "content":prompt}],
        functions:[
            {
                "name": "suggest_utterances",
                "description": "send 3 short, suggested utterances generated from the text",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "a": {
                            "type": "string"
                        },
                        "b": {
                            "type": "string"
                        },
                        "c": {
                            "type": "string"
                        }

                    },
                    "required": ["a", "b", "c"],
                },
            }
        ],
        function_call:{"name": "suggest_utterances"}
    });
    // return JSON.parse(result.data.choices[0].message.content)
    console.log(JSON.stringify(response))

    if(response.choices[0].message.function_call){

        await usageTracker.logUsageFromResponse(model, response)

        return response.choices[0].message.function_call.arguments
    }else{
        return;
    }
   
}

const createSystemPrompt = (locale)=> {


    let baseSystemPrompt = `
    
    Respond in ${locale} as "Being Bevi" a psychology expert knowledgeable in BEVI. Analyze BEVI scores and reports, comprehend user intentions, and correct speech recognition errors. Engage in exploratory, multi-turn dialogues, giving reasoned, layman-friendly explanations. Ask for context and user's opinion after providing insights. Steer non-BEVI topics back to BEVI. Don't mention OpenAI, AI origins, or claim to be a psychologist. Assume BEVI report availability. Ensure responses suit SSML and text-to-speech, in small, readable segments (~40 characters).
    
    // **Never mention scale abbreviations. for example use needs closure instead of NC or (NC)**

    
    `
    baseSystemPrompt+=glossary


    baseSystemPrompt+=`
# **IMPORTANT**
You have been given Bevi scale scores report, do comparative analysis, in report analysis and in group analysis as applicable (the report type is written in the report). Answer questions related to decile profile showing each group member's placement influencing average scale score. In case it is an individual report, answer according to scale scores. 
Address things from most significant to least significant. The user can also see this report on his screen.
*In longitudinal reports, only interpret real-world BEVI scale differences of more than 5 points as meaningful. Less than that is not interpretable. Make sure your math is correct*
`

return baseSystemPrompt;

}

async function fetchData(reportAPI) {
    // let customToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.4wIyF5pSY0oFMb9h0IQI2QgAd67OX3N2cDspqQ_bvGQ";

    // console.log(reportAPI)
    // console.log(`https://${reportAPI.apiPrefix}.takethebevi.com/api/beingbevi/${reportAPI.type}?reportId=${reportAPI.id}`)
    console.log("reportAPI", JSON.stringify(reportAPI))

    try {
        const headers = {
            Authorization: `Bearer ${reportAPI.token}`,
        }; 
        if(reportAPI.apiPrefix !== 'dev' && reportAPI.apiPrefix !== 'apiprod'){
            return
        }
        let response
       
        if( ["LongitudinalIndividualReport", "ComparativeSingleIndividualReport","ComparativeIndividualReportAdmin","PartnerReport","PartnerChangeReport"].includes(reportAPI.type)){
            let reportIds = reportAPI.id.split(",").map((id)=>{return parseInt(id)})
            
            let dataObj = {
                
                "formIds": reportIds,
                "generateArea": "Participant"
                    
            }
            if(reportIds.length <= 1){
                dataObj = {
                
                    "reportId": reportIds[0],
                    "generateArea": "Admin"
                        
                } 
            }
            response = await axios.post(`https://${reportAPI.apiPrefix}.takethebevi.com/api/report/ComparativeIndividualReport`, dataObj, { headers });
            console.log("ComparativeIndividualReport", response.data)
        }else{
            response = await axios.get(`https://${reportAPI.apiPrefix}.takethebevi.com/api/beingbevi/${reportAPI.type}?reportId=${reportAPI.id}`, { headers });
        }
        
        // console.log(response.data);
        return response.data;
    } catch (error) {
    //   console.error(error);
      return error;
    }
}
  


const needsReport = async (messages) => {
    // return [1,1,0]
    let prompt = 
`
There is a psychological report on the user's screen containing BEVI (beliefs, events, and values inventory) scale scores, group and aggregate profile, demographics etc.  

// Only reply with 1 or 0
// does the bot need to see the user's private psych report to answer the user's last message. reply 0 if the prompt is general, FAQ, small talk or context independent; and 1 if the user's report is needed provide tailored guidance.`;   

let res = 0
let result
    try{

         result = await openai.chat.completions.create({
            model: gpt3model,
            temperature:1,
            messages: [{"role":"system", "content":prompt}, ...messages],
        });
        res = result.choices[0].message.content
    }catch(e){
        console.log(e)
    }
    try{
        await usageTracker.logUsageFromResponse(gpt3model, result)
    }
    catch(e){
        console.log(e)
    }
    return res;
    
}


const summarizeHistory = async (history)=>{
    let summaryPrompt = 'Summarize the following conversation to be readable only by gpt4 (human readability not needed) without losing any key aspects, keep a record of all the trivia I have shared about myself. Everything from my age to my dietary habits and anything else I have mentioned  :'+JSON.stringify(history);
    const response = await openai.chat.completions.create({
        model: gpt3model,
        temperature:1,
        messages: [{"role":"system", "content":summaryPrompt}],
    });
    try {
        await usageTracker.logUsageFromResponse(gpt3model, response)
    } catch (error) {
        console.log(error)
    }
    return [{"role":"system", "content":response.choices[0].message.content}] ;
}
const smallTalkPrompt = (locale)=>{
    let prompt = `
    ### Instructions - follow carefully
only respond in **locale: ${locale}**
you are a psychology expert called "Being Bevi" with expertise in BEVI (Beliefs, events, and values inventory). 
you can analyse BEVI reports (individual, group profiles, longitudinal studies) and answer questions about them.
understand user's intention despite speech recognition mistakes.
Respond with a well-reasoned, step-by-step thought-out response. ask clarifying questions, seeking examples for full context.
on user deviating, answer that prompt and then guide user back to BEVI.
never ever mention OpenAI, its AI origins, or claim to be a psychologist.
never ask for report. it is already provided.
**divide the response into smaller more readable chunks of around 40 chars**

${glossary}

// **Never mention scale abbreviations. for example needs closure instead of NC or (NC)**
// Engage in small talk with the user. Prompt the user to ask questions about BEVI, himself, his scale scores etc. 

}    
`;
   return prompt;
}



const apiKey = process.env.OPENAI_API_KEY || "***************************************************";
console.log(apiKey)
// const apiKey = "esecret_cv7qumik1dtu8jfu1syw23qiyl";
// const baseURL = "https://api.endpoints.anyscale.com/v1"
const baseURL = "https://api.openai.com/v1";
  
// const configuration = new Configuration({
//   apiKey: api_key,
// //   organization:"org-vbpJSKWxjY8NThdSh35X5Qty"
// });
// const openai = new OpenAI({
//     apiKey: api_key,
//   //   organization:"org-vbpJSKWxjY8NThdSh35X5Qty"
//   });
const openai = new OpenAI({
    baseURL,
    apiKey,
  //   organization:"org-vbpJSKWxjY8NThdSh35X5Qty"
  });








exports.handler =  async (req, res, context) => {

        console.log(req);
        usageTracker.clearStats(); // Reset usage stats for this request
        jsonData = {}
        decodedToken = {}


        res.write("/nlog:::request recieved "+'/n/n');
        if(req.body === 'init'){
           
              res.end();
              return;
        }else{
            jsonData = JSON.parse(req.body);

        }

        let latestMessage = jsonData['messages'].pop()
        let history = jsonData['messages']
    
    
        if(jsonData.report){
            try{
                decodedToken = jwt_decode(jsonData.report.token);
            }catch(e){
                console.log("invalid token")
            }
            
            console.log(decodedToken);
            let availableInteractions;
            try{
                if(jsonData.isSampleReport){
                    availableInteractions = 1;
                }else{
                    availableInteractions = await getAvailableInteractions(jsonData.report);
                    res.write("/nlog:::interaction balance: "+availableInteractions.toString()+'/n/n');
                }
              
                if(availableInteractions <= 0){
                    res.write("/nlog:::error:::{message:Insufficient balance}");
                    res.end(); 
                    return;
                }
            }catch(e){
                res.write("/nlog:::error:500 ");
                console.log(e);
                res.end(); 
                return;
            }
            
        }else{
            res.write("/nlog:::error:401: "+'Logged out');
            res.end(); 
            return;
        }
    
        
        try {
            if(jsonData['messages'].join('').length > 500){
                history = await summarizeHistory(history);
                res.write("/nlog:::history: "+JSON.stringify(history)+'/n/n');
            }
        } catch (error) {
            console.log(error)
            //exit
            res.write("/nlog:::error:500: ");
            res.end(); 
            // res.status(500).send(e);
            return;
        }
        
        jsonData['stream'] = true;
        let report = "";
        let isReportNeeded
        try {
            // let _needsReportNeeded = await needsReport([
            //     ...history,
            //     latestMessage])

            isReportNeeded = true; //_needsReportNeeded.trim() === "1";
        } catch (error) {
            console.log(error)
            //exit
            // res.status(500).send(e);
            res.write("/nlog:::error:500: "+error);
            res.end(); 
            return;
        }
        
        res.write("/nlog:::isReportNeeded: "+isReportNeeded+'/n');
    
      
    
        if (jsonData.report && isReportNeeded) {
            if (jsonData.report.data && jsonData.report.data.length > 0) {
                report = jsonData.report.data;
                res.write("/nlog:::"+report+'/n/n');
            }else{
                let beviReport = new Report()
                let data
                try{
                    data = await fetchData(jsonData.report)
                    report = beviReport.create(jsonData.report.type, data)
                    
                }catch(e){
                    console.log(e)
                  
                    res.write("/nlog:::error: 401" + "{ message: 'Logged out' }");
                    
                    res.end(); 
                    return;
                }
                
                
            }
        }
       
          
    let systemPrompt;
    let model = gpt3model
    if(!isReportNeeded){
        systemPrompt = smallTalkPrompt(jsonData['locale'])
    }else{
        systemPrompt = createSystemPrompt(jsonData['locale'])
        model = gpt4model
    }
    
    if(report){
        history.push({
            "role": "user",
            "content": `Here is my report:
            ${report}`
        })
    }
    
    
    
    let allMessages = []
    let result = {
        "role": "assistant",
        "content": ""
    }
    let messages = [
    {
        "role": "system",
        "content": systemPrompt
    },
    ...history,
    latestMessage
    ];
    
        
    const inputData = {
        model: model,
        messages: [...messages],
        temperature: 1,
        n: 1,
        stream: true,
        presence_penalty: 0.0,
        frequency_penalty: 0.0,
    
    };
    
    

    let receivedData = ''        
    
    const sendRes = (message, res)=>{
        res.write(message);
    }
    
    const completion = await openai.chat.completions.create(inputData);
    for await (const chunk of completion) {
        let content = chunk.choices[0].delta.content
        if (content && content.length > 0) {
            receivedData += content;
            result.content+=content;
            if (receivedData.includes("\n\n")) {
                let parts = receivedData.split("\n\n");
                for (let i = 0; i < parts.length - 1; i++) {
                    sendRes("{{"+parts[i], res)
                }
                receivedData = parts[parts.length - 1];
            }
        }
    }
    sendRes(receivedData, res);


    messages.push(result)

    let suggestedUtterances = await generateSuggestedUtterances(messages);
    console.log(suggestedUtterances)
    let suggestedUtterancesObject = JSON.parse(suggestedUtterances)


    let suggestedUtteranceString = "//"+suggestedUtterancesObject.a+'|'+suggestedUtterancesObject.b+'|'+suggestedUtterancesObject.c;
    sendRes(suggestedUtteranceString, res);

    console.log("model: '",model,"'")
    try{
        const tokenInfo = usageTracker.calculateTokensFromMessages(model, messages);
        await usageTracker.logUsage(model, tokenInfo.promptUsedTokens, tokenInfo.completionUsedTokens);
    }catch(e){
        console.log(e)
    }

    const usageStats = usageTracker.getUsageStats();
    const totalCost = usageTracker.getTotalCost();

    res.write("/nlog:::usage stats" + JSON.stringify(usageStats, null, 2));
    res.write("/nlog:::Dollar usage for last" + totalCost);

    if(!jsonData.isSampleReport){
        await useInteraction(jsonData.report, usageStats)
    }

    

    res.end(); 

}

