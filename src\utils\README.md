# Usage Tracker Utility

The `UsageTracker` utility provides a centralized way to track OpenAI API usage, calculate costs, and manage usage statistics across different handlers in the application.

## Features

- **Token counting and cost calculation** for various OpenAI models
- **Automatic pricing detection** based on model names
- **Usage statistics tracking** with timestamps
- **Summary reports** for total usage and costs
- **Support for both CommonJS and ES modules**

## Available Files

- `usageTracker.js` - CommonJS version for use with `require()`
- `usageTracker.mjs` - ES module version for use with `import`
- `usageTracker.test.js` - Test file to verify functionality

## Usage

### CommonJS (Node.js with require)

```javascript
const UsageTracker = require('../utils/usageTracker');

const tracker = new UsageTracker();

// Log usage manually
await tracker.logUsage('gpt-4o-mini', 100, 50);

// Log usage from OpenAI response object
const response = await openai.chat.completions.create(params);
await tracker.logUsageFromResponse('gpt-4o-mini', response);

// Get usage statistics
const stats = tracker.getUsageStats();
const totalCost = tracker.getTotalCost();
const summary = tracker.getUsageSummary();
```

### ES Modules (with import)

```javascript
import { UsageTracker } from '../utils/usageTracker.mjs';

const tracker = new UsageTracker();

// Same API as CommonJS version
await tracker.logUsage('gpt-4o-mini', 100, 50);
```

## API Reference

### Constructor

```javascript
const tracker = new UsageTracker();
```

Creates a new UsageTracker instance with empty usage statistics.

### Methods

#### `logUsage(model, inputTokens, outputTokens, additionalData = {})`

Logs usage statistics for a given model and token counts.

- `model` (string): The OpenAI model name
- `inputTokens` (number): Number of input tokens used
- `outputTokens` (number): Number of output tokens generated
- `additionalData` (object): Optional additional data to include in the usage entry

Returns: Promise resolving to the usage entry object

#### `logUsageFromResponse(model, response, additionalData = {})`

Logs usage from an OpenAI API response object.

- `model` (string): The OpenAI model name
- `response` (object): OpenAI API response object with `usage` property
- `additionalData` (object): Optional additional data to include

Returns: Promise resolving to the usage entry object

#### `calculateCost(model, inputTokens, outputTokens)`

Calculates the cost for given token usage.

- `model` (string): The OpenAI model name
- `inputTokens` (number): Number of input tokens
- `outputTokens` (number): Number of output tokens

Returns: Cost in USD (number)

#### `getModelPricing(model)`

Gets the pricing information for a specific model.

- `model` (string): The OpenAI model name

Returns: Object with `input` and `output` pricing per token

#### `getUsageStats()`

Returns: Array of all usage entries

#### `getTotalCost()`

Returns: Total cost across all logged usage (number)

#### `getUsageSummary()`

Returns: Object with comprehensive usage summary including:
- `totalRequests`: Number of API calls tracked
- `totalTokens`: Total tokens used
- `totalInputTokens`: Total input tokens
- `totalOutputTokens`: Total output tokens
- `totalCost`: Total cost in USD
- `averageCostPerRequest`: Average cost per API call
- `usageStats`: Array of all usage entries

#### `clearStats()`

Clears all usage statistics.

#### `calculateTokensFromMessages(model, messages)` (CommonJS only)

Calculates token usage from message arrays using the GPTTokens library.

- `model` (string): The OpenAI model name
- `messages` (array): Array of message objects

Returns: Object with `promptUsedTokens`, `completionUsedTokens`, and `totalTokens`

## Supported Models

The utility includes pricing for the following models:

- `gpt-4-1106-preview`
- `gpt-4o-2024-08-06`
- `gpt-3.5-turbo-1106`
- `gpt-4o-mini`
- `gpt-4`
- `gpt-4.1-2025-04-14`

For unknown models, default pricing is used.

## Usage in Handlers

### Completions Handler (completions.js)

```javascript
const UsageTracker = require('../utils/usageTracker');

exports.handler = async (req, res, context) => {
    const usageTracker = new UsageTracker();
    
    // ... your OpenAI API calls ...
    
    // Log usage from response
    await usageTracker.logUsageFromResponse(model, response);
    
    // Get statistics for logging/reporting
    const stats = usageTracker.getUsageStats();
    const totalCost = usageTracker.getTotalCost();
    
    res.write("/nlog:::usage stats" + JSON.stringify(stats, null, 2));
    res.write("/nlog:::Dollar usage for last" + totalCost);
};
```

### Insights Handler (insights.mjs)

```javascript
import { UsageTracker } from '../utils/usageTracker.mjs';

export async function handler(event, context) {
    const usageTracker = new UsageTracker();
    
    // ... your OpenAI API calls ...
    
    await usageTracker.logUsageFromResponse(model, resp);
    
    return {
        statusCode: 200,
        body: JSON.stringify({
            text: reportText,
            usage: resp.usage,
            usageStats: usageTracker.getUsageStats(),
            totalCost: usageTracker.getTotalCost()
        })
    };
}
```

## Testing

Run the test file to verify functionality:

```bash
node src/utils/usageTracker.test.js
```

This will test all major functionality including usage logging, cost calculation, and statistics generation.
