const AWS = require('aws-sdk');

// Configure the region and credentials
AWS.config.update({
  region: "us-east-1", // Change to your preferred region
  accessKeyId:"********************",
  secretAccessKey:"CjFvFpifD3/y0yxocsfu4wFfMccJZw/Xq0WrJ7tb"
  // accessKeyId and secretAccessKey can also be set here if needed
});

const dynamodb = new AWS.DynamoDB.DocumentClient();

const TABLE_NAME = "UserBalances";

async function getBalance(userId) {
  const params = {
    TableName: TABLE_NAME,
    Key: { userId }
  };

  try {
    const result = await dynamodb.get(params).promise();
    if (result.Item) {
      return result.Item.usd;
    } else {
      await addBalance(userId, 5);  // If user not found, add them with default 5 usd
      return 5;
    }
  } catch (error) {
    console.error("Error getting balance:", error);
    throw error;
  }
}

async function useBalance(userId, amount) {
  const currentBalance = await getBalance(userId);
//   if (currentBalance < amount) {
//     throw new Error("Insufficient balance");
//   }

  return await addBalance(userId, currentBalance - amount);
}

async function addBalance(userId, amount) {
  const params = {
    TableName: TABLE_NAME,
    Item: { 
      userId,
      usd: amount
    }
  };

  try {
    await dynamodb.put(params).promise();
    return amount;
  } catch (error) {
    console.error("Error adding balance:", error);
    throw error;
  }
}
module.exports = {
    getBalance,
    useBalance,
    addBalance
  };
// // Testing the functions
// (async function() {
//   console.log("Initial balance:", await getBalance("user123"));
//   console.log("Balance after adding 10:", await addBalance("user123", 10));
//   console.log("Balance after using 5:", await useBalance("user123", 5));
//   console.log("Final balance:", await getBalance("user123"));
// })();
