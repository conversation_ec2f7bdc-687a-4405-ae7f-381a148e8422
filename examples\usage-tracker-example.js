/**
 * Example demonstrating how to use the UsageTracker utility
 * This shows the before/after comparison of the refactoring
 */

const UsageTracker = require('../src/utils/usageTracker');

// BEFORE: Mixed usage tracking logic (from original completions.js)
function oldLogUsage(model, inputTokens, outputTokens) {
    console.log("inputTokens", inputTokens);
    console.log("outputTokens", outputTokens);
    console.log("model", model);
    
    let inputUSD = 0.0015/1000;
    let outputUSD = 0.002/1000;
    
    if(model.includes("gpt-4-1106-preview")){
        console.log("--gpt-4-1106-preview");
        inputUSD = 0.01/1000;
        outputUSD = 0.03/1000;
    } else if(model.includes("gpt-4o-2024-08-06")){
        console.log("--gpt-4o-2024-08-06");
        inputUSD = 0.0025/1000;
        outputUSD = 0.01/1000;
    } else if(model.includes("gpt-3.5-turbo-1106")){
        console.log("--gpt-3.5-turbo-1106");
        inputUSD = 0.0010/1000;
        outputUSD = 0.0020/1000;
    } else if(model.includes("gpt-4o-mini")){
        console.log("--gpt-4o-mini");
        inputUSD = 0.00015/1000;
        outputUSD = 0.0006/1000;
    } else if(model.includes("gpt-4")){
        console.log("--gpt-4");
        inputUSD = 0.03/1000;
        outputUSD = 0.06/1000;
    }
    
    let usd = (inputTokens*inputUSD)+(outputTokens*outputUSD);
    console.log('Price USD: ', usd);
    
    return {
        "NumToken": (outputTokens+inputTokens),
        "ReportModel": model,
        "OutputToken": outputTokens,
        "InputToken": inputTokens,
        "DolarSpend": usd
    };
}

// AFTER: Clean usage tracking with UsageTracker utility
async function newLogUsage(model, inputTokens, outputTokens) {
    const tracker = new UsageTracker();
    return await tracker.logUsage(model, inputTokens, outputTokens);
}

// Example usage comparison
async function demonstrateRefactoring() {
    console.log('=== Usage Tracking Refactoring Demo ===\n');
    
    const model = 'gpt-4o-mini';
    const inputTokens = 1000;
    const outputTokens = 500;
    
    console.log('BEFORE (old mixed logic):');
    const oldResult = oldLogUsage(model, inputTokens, outputTokens);
    console.log('Result:', JSON.stringify(oldResult, null, 2));
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    console.log('AFTER (clean UsageTracker):');
    const newResult = await newLogUsage(model, inputTokens, outputTokens);
    console.log('Result:', JSON.stringify(newResult, null, 2));
    
    console.log('\n=== Additional UsageTracker Features ===\n');
    
    const tracker = new UsageTracker();
    
    // Log multiple usage entries
    await tracker.logUsage('gpt-4o-mini', 100, 50);
    await tracker.logUsage('gpt-4', 200, 100);
    await tracker.logUsage('gpt-3.5-turbo-1106', 300, 150);
    
    // Get comprehensive summary
    const summary = tracker.getUsageSummary();
    console.log('Usage Summary:');
    console.log(JSON.stringify(summary, null, 2));
    
    console.log('\n=== Model Pricing Information ===\n');
    
    const models = ['gpt-4o-mini', 'gpt-4', 'gpt-3.5-turbo-1106', 'unknown-model'];
    models.forEach(model => {
        const pricing = tracker.getModelPricing(model);
        console.log(`${model}: Input $${pricing.input}/token, Output $${pricing.output}/token`);
    });
    
    console.log('\n=== Cost Calculation ===\n');
    
    const testTokens = { input: 1000, output: 500 };
    models.slice(0, 3).forEach(model => {
        const cost = tracker.calculateCost(model, testTokens.input, testTokens.output);
        console.log(`${model}: $${cost.toFixed(6)} for ${testTokens.input} input + ${testTokens.output} output tokens`);
    });
}

// Run the demonstration
if (require.main === module) {
    demonstrateRefactoring().catch(console.error);
}

module.exports = { demonstrateRefactoring };
