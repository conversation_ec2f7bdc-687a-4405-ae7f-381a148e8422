{"name": "replaced-by-user-input", "description": "replaced-by-user-input", "version": "0.0.1", "private": true, "dependencies": {"aws-sdk": "^2.1451.0", "axios": "^1.4.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "gpt-tokens": "^1.3.10", "jwt-decode": "^3.1.2", "openai": "^5.8.1"}, "devDependencies": {"jest": "^29.2.1"}, "scripts": {"test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "deploy": "node deploy-script.js", "deploy-dev": "sam build && sam deploy --stack-name bev-dev --parameter-overrides Environment=dev", "deploy-prod": "sam build && sam deploy --stack-name bev-prod --parameter-overrides Environment=prod"}, "jest": {"testMatch": ["**/__tests__/**/*.[jt]s?(x)", "**/?(*.)+(spec|test).[jt]s?(x)", "**/__tests__/**/*.mjs?(x)", "**/?(*.)+(spec|test).mjs?(x)"], "moduleFileExtensions": ["mjs", "js"]}}