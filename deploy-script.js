// require('dotenv').config();

// const exec = require('child_process').exec;

// const environment = process.env.ENVIRONMENT;

// const command = environment === 'prod'
//   ? 'sam deploy --stack-name bev-prod --parameter-overrides Environment=prod'
//   : 'sam deploy --stack-name bev-dev --parameter-overrides Environment=dev';

// exec(command, (err, stdout, stderr) => {
//   if (err) {
//     console.error(`Execution error: ${err}`);
//     return;
//   }
//   console.log(stdout);
//   console.error(stderr);
// });


const { spawn } = require('child_process');
require('dotenv').config();

const environment = process.env.ENVIRONMENT;

const deployCommand = environment === 'prod'
  ? ['deploy', '--stack-name', 'bev-prod', '--parameter-overrides', 'Environment=prod']
  : ['deploy', '--stack-name', 'bev-dev', '--parameter-overrides', 'Environment=dev'];

const samDeploy = spawn('sam', deployCommand, { shell: true });


samDeploy.stdout.on('data', (data) => {
    console.log(`stdout: ${data}`);
});

samDeploy.stderr.on('data', (data) => {
    console.error(`stderr: ${data}`);
});

samDeploy.on('close', (code) => {
    console.log(`child process exited with code ${code}`);
});